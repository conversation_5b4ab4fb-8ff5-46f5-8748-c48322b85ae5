{"arrowParens": "always", "bracketSpacing": true, "endOfLine": "lf", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "singleAttributePerLine": true, "bracketSameLine": true, "jsxBracketSameLine": false, "jsxSingleQuote": false, "printWidth": 80, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "all", "useTabs": false, "embeddedLanguageFormatting": "auto", "vueIndentScriptAndStyle": false, "experimentalTernaries": true}