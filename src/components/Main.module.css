main {
  margin-top: 2em;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.mainText {
  mix-blend-mode: difference;
  text-shadow: var(--text-inner-shadow);
  color: var(--text-color-secondary);
  text-align: center;
}

.mainTextP {
  mix-blend-mode: luminosity;
  text-shadow: none;
  color: var(--text-primary-color);
  text-align: center;
}

.mainContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 1200px;
  padding: var(--padding-6);
}

.loaderImageWrapper {
  position: fixed;
  left: 50%;
  top: 70%;
  transform: translateX(-50%);
  z-index: -1;
  width: 100vw;
  display: flex;
  justify-content: center;
  pointer-events: none;
}

.loaderImage {
  width: 100%;
  max-width: clamp(300px, 75vw, 1500px);
  height: auto;
  margin-bottom: 0;
  pointer-events: none;
}
