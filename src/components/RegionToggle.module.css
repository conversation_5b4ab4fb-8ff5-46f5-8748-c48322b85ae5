.container {
  display: inline-flex;
  background: #e7e1f008;
  border-radius: 16px;
  padding: 6px;
  border: 1px solid rgba(249, 248, 239, 0.08);
  gap: 4px;
  justify-content: space-between;
  width: 100%;
}

.button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  padding: 8px 16px;
  border: none;
  background: transparent;

  border-radius: 16px;
  font-family: var(--body-font);
  font-size: var(--body-size);
  color: rgba(231, 225, 240, 0.2);
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
  width: -webkit-fill-available;
}

.button:hover {
  background: rgba(231, 224, 240, 0.25);
}

.active {
  background: rgba(231, 224, 240, 0.05);
  border: 1px solid rgba(231, 224, 240, 0.05);
  color: var(--text-color);
}
