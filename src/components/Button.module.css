.btn {
  display: flex;
  align-items: center;
  padding: clamp(0.5rem, 1vw + 0.25rem, 1rem) clamp(1rem, 2vw + 0.5rem, 2rem);
  border: none;
  gap: clamp(0.25rem, 0.8vw, 1rem);
  border-radius: 9999px;
  background: var(--background-button);
  color: var(--dark-color);
  font-size: clamp(0.75rem, 1vw + 0.25rem, 1.25rem);
  font-family: var(--outfit-regular), sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, opacity 0.2s, fill 0.2s ease-in-out;
  margin-top: clamp(0.75rem, 1.5vw, 2rem);
}

.btn:hover:not(:disabled) {
  opacity: 0.7;
}

.btn:active:not(:disabled) {
  opacity: 0.9;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.icon {
  display: flex;
  align-items: center;
  width: 1.2em;
  height: 1.2em;

  & svg path {
    fill: var(--dark-color);
  }
}
