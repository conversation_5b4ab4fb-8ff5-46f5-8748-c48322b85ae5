.accordion {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.item {
  background: rgba(23, 19, 16, 0.6);
  border: 1px solid rgba(201, 195, 210, 0.3);
  border-radius: 12px;
  overflow: hidden;
  transition: background 0.2s;
}

.item:hover {
  background: rgba(23, 19, 16, 0.7);
}

.question {
  width: 100%;
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  text-align: center;
  align-items: center;
  font-family: var(--emphasis), sans-serif;
  font-size: 1rem;
  color: var(--text-color-secondary);
  background: transparent;
  border: none;
  cursor: pointer;
}

.toggleIcon {
  font-size: 1.25rem;
  line-height: 1;
}

.answer {
  padding: 0.75rem 1rem;
  color: var(--text-color);
  border-top: 1px solid rgba(201, 195, 210, 0.2);
}
