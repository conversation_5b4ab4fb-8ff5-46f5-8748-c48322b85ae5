.card {
  background: var(--dark-color);
  border-radius: 20px;
  padding: 4px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  will-change: transform, opacity;
}

.cardDisabled {
  opacity: 0.6 !important;
  filter: grayscale(60%) !important;
  pointer-events: none !important;
  cursor: not-allowed !important;
}

.cardDisabled .price {
  filter: blur(5px);
}

.cardDisabled * {
  pointer-events: none !important;
}

.cardImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  pointer-events: none;
  border-radius: 16px;
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
}

.cardContent {
  display: flex;
  flex-direction: column;
  gap: clamp(4px, 1vw, 8px);
  width: 100%;
  padding: 0 var(--padding-1);
}

.cardTitle {
  font-size: clamp(1rem, 2.5vw, 1.25rem);
  font-weight: 500;
  color: #fff;
  margin: 0;
  font-family: var(--emphasis), sans-serif;
}

.cardRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: clamp(0.1rem, 1vw, 0.5rem);
}

.status {
  display: flex;
  align-items: center;
  font-size: clamp(0.625rem, 1.5vw, 0.75rem);
  font-weight: medium;
  border-radius: 4px;
  padding: 2px 4px;
  gap: clamp(2px, 0.5vw, 4px);
  pointer-events: none;
}

.statusUpToDate {
  background: #34c75933;
  color: #34c759;
}

.statusInMaintenance {
  background: #ffcc0033;
  color: #ffcc00;
}

.statusExpired {
  background: #ff3b3033;
  color: #ff3b30;
}

.statusSoon {
  background: #7f7f7f33;
  color: #7f7f7f;
}

.price {
  color: #b590ff;
  margin: clamp(0.1rem, 1vw, 0.5rem) 0;
  font-family: var(--emphasis), sans-serif;
  font-size: clamp(0.875rem, 2vw, 1rem);
  opacity: 0.8;
}

.cardFooter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.dash {
  width: 100%;
  border: none;
  border-top: 1px dashed rgba(201, 195, 210, 0.3);
}

.purchaseButton {
  border-radius: 14px 7px 14px 7px !important;
  font-size: clamp(0.75rem, 1.5vw, 0.875rem) !important;
  padding: clamp(4px, 1vw, 8px) clamp(8px, 2vw, 12px) !important;
  box-shadow: var(--text-inner-shadow);
  margin-top: 0 !important;
  transform: none;
  gap: clamp(2px, 0.5vw, 4px) !important;
}

@media (max-width: 600px) {
  .card {
    border-radius: 38px;
  }
}
