.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--padding-4) var(--padding-6);

  .logoIcon {
    width: clamp(5rem, 5vw + 1rem, 6rem);
    height: auto;
    transition: transform 0.25s cubic-bezier(0.4, 1.4, 0.4, 1), box-shadow 0.25s,
      filter 0.25s;
  }

  .logoIcon:hover {
    transform: scale(1.08) rotate(-2deg);
    filter: brightness(1.5) drop-shadow(0 6px 12px var(--primary-color));
    box-shadow: 0 2px 16px 0 rgba(80, 0, 255, 0.12);
  }
}

.nav {
  display: flex;
  align-items: center;
  gap: var(--gap-4);
}

.link {
  display: flex;
  align-items: center;
  gap: var(--gap-1);
  color: var(--text-color-secondary);
  text-decoration: none;
  transition: color 0.2s;
}

.link:hover {
  color: var(--primary-color);
}

.link:hover .icon {
  fill: var(--primary-color);
}

@media (max-width: 600px) {
  .header {
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: var(--padding-2) var(--padding-2);
    gap: 0;
  }

  .logoIcon {
    width: 5em;
    margin-bottom: 8px;
    display: block;
    margin-left: auto;
    margin-right: auto;
    cursor: pointer;
  }
  .nav {
    flex-direction: row;
    gap: var(--gap-2);
    width: 100%;
    justify-content: space-between;
    margin-top: 4px;
  }
  .link {
    font-size: 1.1rem;
    gap: 2px;
    padding: 2px 0;
  }
  .icon {
    width: 1.3rem;
    height: 1.3rem;
  }
}
