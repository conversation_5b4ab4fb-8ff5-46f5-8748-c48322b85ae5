:global(.background-gradient) {
  display: none !important;
}

:global(html),
:global(body) {
  overflow-x: hidden;
}

body {
  background-color: #181624;
}

.container {
  margin: 0 auto;
  padding: clamp(16px, 3vw, 64px);
}

.columns {
  display: grid;
  grid-template-columns: minmax(0, 2fr) minmax(0, 1fr);
  gap: clamp(16px, 3vw, 64px);
  width: 100%;
}

.content {
  background: rgba(23, 19, 16, 0.6);
  border: 1px solid rgba(249, 248, 239, 0.2);
  border-radius: clamp(15px, 2vw, 30px);
  gap: clamp(16px, 2.5vw, 48px);
  padding: clamp(16px, 3vw, 64px);
  font-size: clamp(0.75rem, 1.5vw, 1.2rem);
  color: var(--text-color);
  backdrop-filter: blur(200px);
  -webkit-backdrop-filter: blur(200px);
  width: 100%; /* Ensure full width within grid */
}

.productHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title {
  display: flex;
  align-items: baseline;
  gap: clamp(4px, 1vw, 12px);
  font-family: var(--title), sans-serif;
  font-size: clamp(1.5rem, 3vw, 3rem);
  color: var(--text-color-secondary);
}

.section {
  font-size: clamp(24px, 3.5vw, 54px);
  font-weight: bold;
  color: #fff;
  font-family: 'Outfit Semibold', sans-serif;
}

.separator {
  font-family: 'Outfit Semibold', sans-serif;
  font-size: clamp(18px, 2.5vw, 36px);
  color: #fff;
}

.subtitle {
  font-family: 'Kode mono', monospace;
  font-size: clamp(14px, 1.8vw, 24px);
  color: var(--text-color);
}

.status {
  display: inline-block;
  padding: clamp(4px, 1vw, 12px) clamp(8px, 1.5vw, 20px);
  border-radius: clamp(12px, 1.8vw, 24px);
  font-size: clamp(12px, 1.2vw, 18px);
  font-weight: var(--outfit-medium);
  color: #34c759;
  background: rgba(52, 199, 89, 0.2);
}

.statusExpired {
  color: #ff3b30;
  background: rgba(255, 59, 48, 0.2);
}

.statusInMaintenance {
  color: #ffcc00;
  background-color: rgba(255, 204, 0, 0.2);
}

.statusSoon {
  color: #7f7f7f;
  background-color: rgba(127, 127, 127, 0.2);
}

.tags {
  margin-top: clamp(12px, 2vw, 32px);
  display: flex;
  gap: clamp(6px, 1.2vw, 16px);
}

.tag {
  display: inline-block;
  padding: clamp(3px, 0.8vw, 8px) clamp(6px, 1.2vw, 16px);
  border: 1px solid #ff5b5b;
  border-radius: clamp(6px, 1.2vw, 12px);
  font-size: clamp(12px, 1.2vw, 18px);
  font-weight: 600;
  color: #ff5b5b;
  background: transparent;
}

.paragraph {
  margin: clamp(6px, 1vw, 16px) 0 0;
  color: var(--text-color);
  opacity: 0.6;
  font-size: clamp(12px, 1.2vw, 18px);
}

.divider {
  border: none;
  border-top: 1px solid rgba(249, 248, 239, 0.2);
  margin: clamp(12px, 2vw, 32px) 0;
}

.warning {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  margin-top: clamp(12px, 2vw, 32px);
  padding-inline: clamp(6px, 1.2vw, 16px);
  border-left: 5px solid rgba(255, 59, 48);
}
.warning strong {
  margin-right: clamp(6px, 1.2vw, 16px);
  color: rgba(255, 59, 48);
  font-weight: 700;
}
.warning p {
  margin: 0;
  color: var(--text-color);
  font-size: clamp(0.8rem, 1.2vw, 1.2rem);
  opacity: 0.6;
}

/* Platforms */
.platforms {
  margin-top: clamp(12px, 2vw, 32px);
}
.platforms strong {
  display: block;
  margin-bottom: clamp(6px, 1.2vw, 16px);
  color: var(--text-color);
}
.platforms p {
  margin: 0;
  color: var(--text-color);
  opacity: 0.6;
}

/* Preview images */
.preview {
  display: flex;
  gap: clamp(12px, 2vw, 32px);
  margin-top: clamp(12px, 2vw, 32px);
}
.preview img {
  width: calc(50% - clamp(4px, 1vw, 16px));
  border-radius: clamp(8px, 1.2vw, 14px);
  object-fit: cover;
}

/* RIGHT PANEL */
.checkout {
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: space-between;
  background: rgba(27, 26, 32, 0.85);
  border-radius: clamp(15px, 2vw, 30px);
  padding: clamp(16px, 3vw, 64px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(30px);
  width: 100%; /* Ensure full width within grid */
}

.checkoutHeader {
  display: flex;
  gap: clamp(3px, 0.8vw, 8px);
  align-items: center;
}

.checkoutIcon {
  height: 1em;
  width: auto;
}

.checkoutHeading {
  font-size: clamp(18px, 2.5vw, 32px);
  color: var(--text-color-secondary);
  font-weight: bold;
}

.total {
  text-align: right;
  color: rgba(231, 225, 240, 0.9);
  font-family: var(--outfit-semi-bold);
  font-size: clamp(36px, 4.5vw, 72px);
}

.paymentBtn {
  width: 100%;
  padding: clamp(10px, 1.8vw, 20px) 0;
  background: #e7e1f0;
  border: none;
  border-radius: 999px;
  font-size: clamp(0.9rem, 1.5vw, 1.4rem);
  font-weight: 600;
  color: #1a1a1a;
  cursor: pointer;
  display: flex;
  justify-content: center;
  box-shadow: 0px 182px 51px rgba(231, 225, 240, 0),
    0px 116px 47px rgba(231, 225, 240, 0.01),
    0px 165px 39px rgba(231, 225, 240, 0.05),
    0px 29px 29px rgba(231, 225, 240, 0.09),
    0px 7px 16px rgba(231, 225, 240, 0.1);
}

.terms {
  align-self: center;
  margin-top: clamp(12px, 2vw, 32px);
  font-size: clamp(0.7rem, 1.2vw, 1rem);
  color: var(--text-color);
}

/* ========== Responsive ========== */

@media (max-width: 1024px) {
  .container {
    padding: clamp(16px, 2vw, 32px);
  }
  .columns {
    grid-template-columns: 1fr;
    gap: clamp(16px, 2vw, 32px);
  }
  .checkout {
    padding: clamp(16px, 2vw, 32px);
  }
}

/* Phone */
@media (max-width: 600px) {
  .container {
    padding: 16px;
  }
  .title {
    font-size: clamp(1.2rem, 3vw, 1.5rem);
  }
  .separator {
    font-size: clamp(16px, 2.5vw, 20px);
  }
  .preview {
    flex-direction: column;
    gap: clamp(8px, 1.5vw, 12px);
  }
  .preview img {
    width: 100%;
  }
  .checkout {
    padding: 16px;
  }
}
