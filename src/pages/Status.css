html,
body {
  margin: 0;
}
#status {
  position: fixed;
  inset: 0;
  padding: var(--padding-6);
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.status-container {
  width: 100%;
  max-width: 1000px;
  background: rgba(23, 19, 16, 0.6);
  border: 1px solid rgba(249, 248, 239, 0.2);
  border-radius: 20px;
  padding: 16px;
  backdrop-filter: blur(200px);
  -webkit-backdrop-filter: blur(200px);
  box-sizing: border-box;
}

.status-container h1 {
  margin: 0 0 var(--padding-4);
  color: var(--text-color-secondary);
  font-size: 72px;
}

.status-table-wrapper {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.status-table {
  width: 100%;
  min-width: 800px;
  border-collapse: collapse;
}

.status-table thead th {
  text-align: left;
  padding: var(--padding-2) var(--padding-3);
  color: var(--text-color-secondary);
  font-size: 24px;
}

.status-table tbody tr td {
  padding: var(--padding-2) var(--padding-3);
  border-top: 1px solid rgba(249, 248, 239, 0.2);
  color: var(--text-color);
  font-size: 16px;
}

.loader-icon {
  width: 18px;
  height: 18px;
  object-fit: contain;
  margin-right: 8px;
  vertical-align: middle;
}

.header-icon {
  width: 1em;
  height: 1em;
  vertical-align: middle;
  margin-right: var(--gap-1);
  fill: var(--text-color-secondary);
}

.status-pill {
  display: inline-flex;
  align-items: center;
  gap: var(--gap-1);
  font-size: clamp(0.75rem, 1vw + 0.25rem, 1rem);
  font-weight: 500;
  border-radius: 4px;
  padding: 4px 8px;

  & svg {
    width: 1em;
    height: 1em;
    vertical-align: middle;
    margin-right: 0.25em;
    fill: currentColor;
  }
}
.status-up-to-date {
  background: #34c75933;
  color: #34c759;
}
.status-in-maintenance {
  background: #ffcc0033;
  color: #ffcc00;
}
.status-soon {
  background: #7f7f7f33;
  color: #7f7f7f;
}

@media (max-width: 600px) {
  .status-container {
    padding: 12px;
  }
  .status-container h1 {
    font-size: 48px;
  }
  .status-table thead th,
  .status-table tbody td {
    font-size: 14px;
    padding: 8px 12px;
  }
  .status-pill {
    font-size: 0.75rem;
    padding: 2px 6px;
  }
  .header-icon {
    width: 1em;
    height: 1em;
  }
}
