.resources {
  width: 100%;
  margin: 0;
  min-height: calc(100vh - 120px);
  max-height: calc(100vh - 120px);
  overflow-y: hidden;
  display: flex;
  flex-direction: column;
  padding: 36px;
}

.title {
  font-family: var(--title), sans-serif;
  font-size: clamp(2.5rem, 6vw, 6rem);
  color: var(--text-color-secondary);
  margin-bottom: clamp(24px, 3vw, 48px);
  text-align: left;
}

.cards {
  display: flex;
  gap: clamp(16px, 3vw, 48px);
  flex: 1;
  width: 100%;
}

.left,
.right {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.left > .card,
.right > .card {
  flex: 1;
  width: 100%;
  min-height: 0;
}

.right {
  justify-content: space-between;
  gap: clamp(24px, 3.5vw, 48px);
}

.links {
  display: flex;
  flex-direction: column;
  gap: clamp(8px, 1.5vw, 16px);
  padding-inline: clamp(16px, 3vw, 48px);
}

.card {
  background: rgba(23, 19, 16, 0.6);
  backdrop-filter: blur(200px);
  -webkit-backdrop-filter: blur(200px);
  border: 1px solid var(--status-soon);
  border-radius: clamp(20px, 3vw, 40px);
  display: flex;
  flex-direction: column;
  padding: clamp(16px, 2.5vw, 32px);
  min-height: clamp(200px, 25vw, 350px);
  overflow-y: auto;
}

.cardTitle {
  margin: 0 0 clamp(10px, 1.5vw, 20px) 0;
  font-family: var(--subtitle1), sans-serif;
  font-size: clamp(1.5rem, 3vw, 3.5rem);
  color: var(--text-color-secondary);
}

.cardDescription {
  margin: 0 0 clamp(15px, 2vw, 25px) 0;
  font-family: var(--body), sans-serif;
  font-size: clamp(1.2rem, 2.5vw, 2.5rem);
  color: var(--text-color);
  flex: 1;
  overflow-wrap: break-word;
}

.cardLink {
  font-family: var(--body), sans-serif;
  font-size: clamp(1.1rem, 2.5vw, 1.8rem);
  text-decoration: underline;
  color: var(--text-color-secondary);
  align-self: flex-start;
  margin-top: auto;
}

.footerLink {
  font-family: 'Kode mono', monospace;
  font-size: clamp(1.1rem, 2.5vw, 1.8rem);
  font-weight: 700;
  text-decoration: underline;
  color: var(--text-color);
}

@media (max-width: 1024px) {
  .resources {
    height: auto;
    max-height: none;
    padding: clamp(12px, 2.5vw, 20px);
  }
  .cards {
    flex-direction: column;
    height: auto;
  }
  .title {
    font-size: clamp(2rem, 5vw, 3.5rem);
  }
  .card {
    padding: clamp(12px, 2vw, 24px);
    min-height: clamp(180px, 22vw, 250px);
  }
  .cardTitle {
    font-size: clamp(1.2rem, 2.5vw, 2rem);
  }
  .cardDescription {
    font-size: clamp(1rem, 2vw, 1.5rem);
  }
  .cardLink {
    font-size: clamp(0.9rem, 1.8vw, 1.2rem);
  }
}

@media (max-width: 600px) {
  .resources {
    padding-inline: clamp(12px, 2.5vw, 20px);
  }
  .title {
    font-size: clamp(1.5rem, 4.5vw, 2.5rem);
    margin-bottom: clamp(16px, 2.5vw, 32px);
  }
  .cards {
    gap: clamp(16px, 2.5vw, 32px);
  }
  .card {
    padding: clamp(12px, 2vw, 20px);
    min-height: clamp(150px, 20vw, 200px);
  }
  .cardTitle {
    font-size: clamp(1rem, 2vw, 1.5rem);
  }
  .cardDescription {
    font-size: clamp(0.9rem, 1.5vw, 1.2rem);
  }
  .cardLink {
    font-size: clamp(0.8rem, 1.5vw, 1rem);
  }
}
