:global(.background-gradient) {
  display: none !important;
}

:global(html),
:global(body) {
  overflow-x: hidden;
}

body {
  background-color: #181624;
}

.container {
  display: flex;
  align-items: center;
  margin: 0 auto;
  padding: 16px 50px;
}

.columns {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 16px;
}

.content {
  background: rgba(23, 19, 16, 0.8);
  border: 1px solid rgba(249, 248, 239, 0.2);
  border-radius: 20px;
  padding: 16px;
  font-size: 0.85rem;
  backdrop-filter: blur(200px);
  -webkit-backdrop-filter: blur(200px);
}

.title {
  display: flex;
  align-items: baseline;
  gap: 6px;
  margin: 0 0 16px;
  font-family: var(--title), sans-serif;
  font-size: 1.75rem;
  color: var(--text-color-secondary);
}

.section {
  font-size: 36px;
  font-weight: bold;
  color: #fff;
  font-family: 'Outfit Semibold', sans-serif;
}

.separator {
  font-family: 'Outfit Semibold', sans-serif;
  font-size: 24px;
  color: #fff;
}

.subtitle {
  font-family: 'Kode mono', monospace;
  font-size: 16px;
  color: var(--text-color);
}

.heading {
  margin: 0;
  font-family: var(--subtitle1), sans-serif;
  font-size: 1.25rem;
  color: var(--text-color);
}

.paragraph {
  margin: 8px 0 0;
  color: var(--text-color);
  opacity: 0.6;
}

.divider {
  border: none;
  border-top: 1px solid rgba(249, 248, 239, 0.2);
  margin: 16px 0;
}

.list {
  margin: 8px 0 0 1rem;
  padding: 0;
  list-style: disc;
  color: var(--text-color);
  opacity: 0.6;
}

.list li + li {
  margin-top: 4px;
}

.note {
  margin-top: 16px;
  font-size: 0.75rem;
  color: var(--text-color);
  opacity: 0.6;
}

.promos {
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: flex-start;
  gap: 16px;
  position: sticky;
  top: 0;
}

@media (max-width: 1024px) {
  .columns {
    grid-template-columns: 1fr;
  }

  .container {
    padding: 25px;
  }

  .title {
    font-size: 2rem;
    margin-bottom: 24px;
  }

  .heading {
    font-size: 1.2rem;
  }

  .paragraph,
  .list,
  .note {
    font-size: 0.8rem;
  }

  .promos :global(.promo) {
    width: 100% !important;
    max-width: none !important;
  }

  .promos {
    position: static;
  }
}

@media (max-width: 600px) {
  .title {
    font-size: 1.75rem;
    margin-bottom: 16px;
  }
}
