.container {
  max-width: 1200px;
  margin: 4rem auto;
  padding: 2rem;
  background: rgba(23, 19, 16, 0.8);
  border: 1px solid rgba(249, 248, 239, 0.2);
  border-radius: 30px;
  backdrop-filter: blur(200px);
  -webkit-backdrop-filter: blur(200px);
}

.title {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.section {
  font-size: 36px;
  font-weight: bold;
  color: #fff;
  font-family: 'Outfit Semibold', sans-serif;
}

.separator {
  font-family: 'Outfit Semibold', sans-serif;
  font-size: 24px;
  color: #fff;
}

.subtitle {
  font-family: 'Kode mono', monospace;
  font-size: 16px;
  color: var(--text-color);
}

.content {
  opacity: 0.6;
  margin: 0 0 1.5rem;
  font-size: var(--body-font-size);
  color: var(--text-color);
  line-height: 1.6;
}
.heading {
  margin: 0;
  font-family: var(--subtitle1), sans-serif;
  font-size: 1.25rem;
  color: var(--text-color);
}

.paragraph {
  margin: 0 0 1.5rem;
  font-size: var(--body-font-size);
  color: var(--text-color);
  line-height: 1.6;
}
.list {
  margin: 0 0 1.5rem 1.5rem;
  padding: 0;
  list-style: disc;
  color: var(--text-color);
  line-height: 1.6;
}
.listItem + .listItem {
  margin-top: 0.5rem;
}
.updated {
  margin: 4rem 0 0;
  font-size: clamp(0.75rem, 1vw + 0.5rem, 1rem);
  color: var(--text-color);
  opacity: 0.8;
}
.container a {
  color: var(--text-color);
  text-decoration: underline;
}
@media (max-width: 1024px) {
  .container {
    margin: 2rem 1rem;
    padding: 1.5rem;
  }
  .heading {
    font-size: 1.1rem;
  }
  .paragraph,
  .list,
  .updated {
    font-size: 0.85rem;
  }
}
@media (max-width: 600px) {
  .container {
    margin: 1rem 25px;
    padding: 1em;
  }
  .section {
    font-size: 2rem;
  }
  .subtitle {
    font-size: 1rem;
  }
}
