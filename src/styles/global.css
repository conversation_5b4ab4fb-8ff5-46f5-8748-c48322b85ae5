* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body,
main,
h1,
p {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: var(--outfit-regular);
  font-size: var(--body-font-size);
  line-height: 1.5;
  background-color: var(--background-color);
  color: var(--text-color);
}

::-webkit-scrollbar {
  width: 8px;
  background: rgba(24, 22, 36, 0.2);
}
::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #2d253a 0%, #3a2e4d 100%);
  border-radius: 8px;
  border: 2px solid rgba(24, 22, 36, 0.2);
}
::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #b98dff 0%, #2d253a 100%);
}
::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox */
html {
  scrollbar-width: thin;
  scrollbar-color: #3a2e4d rgba(24, 22, 36, 0.2);
}
