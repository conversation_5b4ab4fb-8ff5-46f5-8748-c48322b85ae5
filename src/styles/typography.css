/* ---- Title ---- */
.txt-title {
  font-family: var(--title);
  font-size: clamp(2.5rem, 4vw + 1rem, 4.5rem); /* ~40–72px */
  letter-spacing: -0.03em;
  text-shadow: var(--text-inner-shadow);
}

/* ---- Subtitle 1 ---- */
.txt-subtitle1 {
  font-family: var(--subtitle1);
  font-size: clamp(1.75rem, 3vw + 0.5rem, 2.5rem); /* ~28–40px */
  letter-spacing: -0.02em;
  line-height: 1.2;
}

/* ---- Subtitle 2 ---- */
.txt-subtitle2 {
  font-family: var(--subtitle2);
  font-size: clamp(1.5rem, 2.5vw + 0.25rem, 2rem); /* ~24–32px */
  letter-spacing: -0.01em;
  line-height: 1.3;
}

/* ---- Subtitle 3 ---- */
.txt-subtitle3 {
  font-family: var(--subtitle3);
  font-size: clamp(1.25rem, 2vw + 0.25rem, 1.75rem); /* ~20–28px */
  letter-spacing: -0.01em;
  line-height: 1.3;
}

/* ---- Emphasis ---- */
.txt-emphasis {
  font-family: var(--emphasis);
  font-size: clamp(0.875rem, 1.5vw + 0.25rem, 1.125rem); /* ~14–18px */
  letter-spacing: -0.005em;
  line-height: 1.4;
}

/* ---- Body ---- */
.txt-body {
  font-family: var(--body);
  font-size: clamp(0.875rem, 1.5vw + 0.25rem, 1.125rem); /* ~14–18px */
  letter-spacing: 0;
  line-height: 1.6;
}

/* ---- Label ---- */
.txt-label {
  font-family: var(--label);
  font-size: clamp(0.8125rem, 1.25vw + 0.25rem, 1rem); /* ~13–16px */
  letter-spacing: 0.02em;
  line-height: 1.4;
}

/* ---- Caps ---- */
.txt-caps {
  font-family: var(--caps);
  font-size: clamp(0.875rem, 1.5vw + 0.25rem, 1.125rem); /* ~14–18px */
  letter-spacing: 0.1em;
  line-height: 1.3;
  text-transform: uppercase;
}

/* ---- Caption ---- */
.txt-caption {
  font-family: var(--caption);
  font-size: clamp(0.8125rem, 1.25vw + 0.25rem, 1rem); /* ~13–16px */
  letter-spacing: 0.01em;
  line-height: 1.4;
}
