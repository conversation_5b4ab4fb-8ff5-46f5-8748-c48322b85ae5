@import './fonts.css';

:root {
  /* -------------------- Font Families -------------------- */
  --outfit-black: 'Outfit Black';
  --outfit-bold: 'Outfit Bold';
  --outfit-extra-bold: 'Outfit ExtraBold';
  --outfit-extra-light: 'Outfit ExtraLight';
  --outfit-light: 'Outfit Light';
  --outfit-medium: 'Outfit Medium';
  --outfit-regular: 'Outfit Regular';
  --outfit-semi-bold: 'Outfit SemiBold';
  --outfit-thin: 'Outfit Thin';

  /* -------------------- Typography Roles -------------------- */
  --title: var(--outfit-extra-light);
  --subtitle1: var(--outfit-semi-bold);
  --subtitle2: var(--outfit-light);
  --subtitle3: var(--outfit-medium);
  --emphasis: var(--outfit-medium);
  --body: var(--outfit-regular);
  --label: var(--outfit-bold);
  --caps: var(--outfit-semi-bold);
  --caption: var(--outfit-light);

  /* -------------------- Font Sizes -------------------- */
  --body-font-size: clamp(
    0.875rem,
    1.5vw + 0.125rem,
    1.25rem
  ); /* 14px - 20px */
  --caption-font-size: clamp(
    0.75rem,
    1.25vw + 0.125rem,
    1.125rem
  ); /* 12px - 18px */
  --caps-font-size: clamp(
    0.875rem,
    1.5vw + 0.125rem,
    1.25rem
  ); /* 14px - 20px */
  --emphasis-font-size: clamp(
    0.875rem,
    1.5vw + 0.125rem,
    1.25rem
  ); /* 14px - 20px */
  --label-font-size: clamp(
    0.75rem,
    1.25vw + 0.125rem,
    1.125rem
  ); /* 12px - 18px */
  --link-font-size: clamp(0.75rem, 1vw + 0.5rem, 0.8rem);
  --subtitle1-font-size: clamp(1.5rem, 3vw + 0.5rem, 2.5rem); /* 24px - 40px */
  --subtitle2-font-size: clamp(
    1.25rem,
    2.5vw + 0.25rem,
    2rem
  ); /* 20px - 32px */
  --subtitle3-font-size: clamp(
    1.125rem,
    2vw + 0.125rem,
    1.75rem
  ); /* 18px - 28px */
  --title-font-size: clamp(2rem, 4vw + 1rem, 3.5rem);
  --icon-size: clamp(0.75rem, 1vw + 0.5rem, 0.8rem);
  --logo-size: 3rem;

  /* -------------------- Spacing: Gap -------------------- */
  --gap-1: 0.25rem; /* 4px */
  --gap-2: 0.5rem; /* 8px */
  --gap-3: 0.75rem; /* 12px */
  --gap-4: 1rem; /* 16px */
  --gap-5: 1.25rem; /* 20px */
  --gap-6: 1.5rem; /* 24px */
  --gap-7: 1.75rem; /* 28px */
  --gap-8: 2rem; /* 32px */
  --gap-9: 2.25rem; /* 36px */

  /* -------------------- Spacing: Padding -------------------- */
  --padding-1: 0.25rem; /* 4px */
  --padding-2: 0.5rem; /* 8px */
  --padding-3: 0.75rem; /* 12px */
  --padding-4: 1rem; /* 16px */
  --padding-5: 1.25rem; /* 20px */
  --padding-6: 1.5rem; /* 24px */
  --padding-7: 1.75rem; /* 28px */
  --padding-8: 2rem; /* 32px */
  --padding-9: 2.25rem; /* 36px */
  --padding-10: 2.5rem; /* 40px */

  /* -------------------- Colors -------------------- */
  --primary-color: #925bff;
  --text-color: #c9c3d2;
  --text-color-secondary: #f9f8ef;
  --dark-color: #171310;

  /* -------------------- Backgrounds -------------------- */
  --background-color: var(--dark-color);
  --background-button: linear-gradient(
    180deg,
    rgba(231, 225, 240, 1) 0%,
    rgba(231, 225, 240, 0.94) 0%,
    rgba(231, 225, 240, 0.88) 0%,
    rgba(231, 225, 240, 0.75) 100%
  );

  /* -------------------- Effects -------------------- */
  --text-inner-shadow: inset 0px 2px 4px #fff;

  /* -------------------- Status -------------------- */
  --status-soon: #7f7f7f;
  --status-detected: #ff3b30;
  --status-undetected: #5856d6;
  --status-up-to-date: #34c759;
  --status-in-maintenance: #ffcc00;
  --status-updating: #ff9500;
}
